#!/bin/bash

# CareerWorx Layout Organization Script
echo "Organizing layout files..."

BASE_DIR="/Users/<USER>/Desktop/Kotlin App"
cd "$BASE_DIR"

# Create layout subdirectories
echo "Creating layout subdirectories..."
mkdir -p app/src/main/res/layout/admin
mkdir -p app/src/main/res/layout/company
mkdir -p app/src/main/res/layout/student
mkdir -p app/src/main/res/layout/shared

echo "Moving admin layout files..."
# Admin layouts
mv app/src/main/res/layout/activity_admin_dashboard.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/activity_admin_applications.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/activity_admin_companies.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/activity_admin_jobs.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/activity_admin_login.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/activity_admin_users.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_admin_application_details.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_admin_edit_application.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_admin_edit_company.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_admin_edit_job.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_admin_edit_user.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_admin_job_details.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_admin_companies.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/item_admin_application.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/item_admin_company.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/item_admin_job.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/item_admin_user.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/layout_admin_pagination.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/layout_admin_search.xml app/src/main/res/layout/admin/ 2>/dev/null || true
mv app/src/main/res/layout/layout_admin_toolbar.xml app/src/main/res/layout/admin/ 2>/dev/null || true

echo "Moving company layout files..."
# Company layouts
mv app/src/main/res/layout/activity_company_analytics.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_company_application_details.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_company_dashboard.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_company_dashboard_new.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_company_profile.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_company_signup.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_candidate_search.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_edit_company_profile.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_edit_job.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_employer_analytics.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_employer_jobs.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/activity_post_job.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_company_applications.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_company_jobs.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_company_profile.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_company_signup.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/item_candidate_search.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/item_company.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/item_company_application.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/item_employer_job.xml app/src/main/res/layout/company/ 2>/dev/null || true
mv app/src/main/res/layout/layout_candidate_filter_drawer.xml app/src/main/res/layout/company/ 2>/dev/null || true

echo "Moving student layout files..."
# Student layouts
mv app/src/main/res/layout/activity_home.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_profile.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_jobs.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_applications.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_my_applications.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_saved_jobs.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_search.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_job_details.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_job_detail.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_application_details.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_student_application_details.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_view_cv.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_cv_details.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/activity_cv_review.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_student_signup.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/fragment_jobs.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/item_job.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/item_recent_job.xml app/src/main/res/layout/student/ 2>/dev/null || true
mv app/src/main/res/layout/item_application.xml app/src/main/res/layout/student/ 2>/dev/null || true

echo "Moving shared layout files..."
# Shared layouts
mv app/src/main/res/layout/activity_login.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_signup.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_unified_signup.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_splash.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_chat.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_chatbot.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_conversations.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_contact.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_help.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_pdf_viewer.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/activity_user_applications.xml app/src/main/res/layout/shared/ 2>/dev/null || true

# Shared dialogs and components
mv app/src/main/res/layout/dialog_application_details.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_candidate_profile.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_cover_letter.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_edit_application.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_edit_job.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_schedule_meeting.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_search.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dialog_user_application_details.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/bottom_sheet_apply.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/bottom_sheet_cover_letter.xml app/src/main/res/layout/shared/ 2>/dev/null || true

# Shared item layouts
mv app/src/main/res/layout/item_certificate.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_certificate_badge.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_chat_message_bot.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_chat_message_user.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_conversation.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_dropdown.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_education.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_experience.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_filter_dropdown.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_language.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_message_received.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_message_sent.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_recent_activity.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_reference.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_user_application.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/item_work_experience.xml app/src/main/res/layout/shared/ 2>/dev/null || true

# Shared layout components
mv app/src/main/res/layout/layout_filter_drawer.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/layout_no_search_results.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/layout_chatbot_fab.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/toolbar_main.xml app/src/main/res/layout/shared/ 2>/dev/null || true

# Shared dropdown and misc items
mv app/src/main/res/layout/dropdown_item.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/dropdown_menu_item.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/hobby_item.xml app/src/main/res/layout/shared/ 2>/dev/null || true
mv app/src/main/res/layout/skill_item.xml app/src/main/res/layout/shared/ 2>/dev/null || true

echo "Layout organization complete!"
echo ""
echo "Layout files have been organized into:"
echo "- app/src/main/res/layout/admin/ - Admin-specific layouts"
echo "- app/src/main/res/layout/company/ - Company-specific layouts"
echo "- app/src/main/res/layout/student/ - Student-specific layouts"
echo "- app/src/main/res/layout/shared/ - Shared layouts and components"
echo ""
echo "Note: You may need to update layout references in your code if they use hardcoded paths."
