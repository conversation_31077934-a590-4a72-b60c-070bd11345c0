<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:elevation="4dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar"
            app:title="Post a Job"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/jobTitleLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Job Title"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/jobTitleInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/jobFieldLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Job Field"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/jobFieldInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/jobSpecializationLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Specialization"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/jobSpecializationInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/skillsLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Required Skills"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/skillsInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.chip.ChipGroup
                android:id="@+id/selectedSkillsChipGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"/>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/jobTypeLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Job Type"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/jobTypeInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/provinceLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Province"
                app:boxStrokeColor="?attr/colorPrimary">

                <AutoCompleteTextView
                    android:id="@+id/provinceInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/jobCityLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:hint="City"
                app:boxStrokeColor="?attr/colorPrimary">

                <AutoCompleteTextView
                    android:id="@+id/cityInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/salaryLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Salary Range"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/salaryRangeInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/experienceLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Experience Level Required"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/experienceInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/descriptionLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Job Description"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/descriptionInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:minLines="4"
                    android:gravity="top"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/requirementsLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Requirements"
                app:boxStrokeColor="?attr/colorPrimary"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/requirementsInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:minLines="4"
                    android:gravity="top"
                    android:textColor="?android:attr/textColorPrimary"/>

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/postButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="Post Job"
                android:textColor="?attr/colorOnPrimary"
                app:cornerRadius="8dp"
                android:backgroundTint="?attr/colorPrimary"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>