<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/activityCard"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/primary_light"
    app:cardBackgroundColor="@color/surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/activityTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="New Application"
            android:textColor="@color/primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/activityDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Candidate applied for Software Developer position"
            android:textColor="@color/text_primary"
            android:textSize="15sp"
            android:minLines="2"
            android:maxLines="3"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/activityTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="May 15, 2023"
            android:textColor="@color/text_secondary"
            android:textSize="13sp"
            android:textStyle="italic" />
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
