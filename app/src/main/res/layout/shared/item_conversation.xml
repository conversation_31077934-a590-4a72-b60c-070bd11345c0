<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="16dp">

    
    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/profileImageView"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:src="@drawable/ic_company_placeholder"
        app:civ_border_color="?attr/colorOutline"
        app:civ_border_width="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    
    <TextView
        android:id="@+id/participantNameText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        style="@style/Widget.CareerWorx.TextInputEditText"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/timeText"
        app:layout_constraintStart_toEndOf="@+id/profileImageView"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Samsung Electronics" />

    
    <TextView
        android:id="@+id/jobTitleText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?android:attr/textColorSecondary"
        android:textSize="13sp"
        app:layout_constraintEnd_toStartOf="@+id/timeText"
        app:layout_constraintStart_toEndOf="@+id/profileImageView"
        app:layout_constraintTop_toBottomOf="@+id/participantNameText"
        tools:text="Senior Software Engineer" />

    
    <TextView
        android:id="@+id/lastMessageText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?android:attr/textColorSecondary"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@+id/unreadCountText"
        app:layout_constraintStart_toEndOf="@+id/profileImageView"
        app:layout_constraintTop_toBottomOf="@+id/jobTitleText"
        tools:text="Hello, we would like to schedule an interview..." />

    
    <TextView
        android:id="@+id/timeText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?android:attr/textColorSecondary"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="10:30 AM" />

    
    <TextView
        android:id="@+id/unreadCountText"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:background="@drawable/circle_background"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/lastMessageText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/lastMessageText"
        tools:text="3"
        tools:visibility="visible" />

    
    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="66dp"
        android:layout_marginTop="16dp"
        android:background="?attr/colorOutline"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lastMessageText" />

</androidx.constraintlayout.widget.ConstraintLayout>
