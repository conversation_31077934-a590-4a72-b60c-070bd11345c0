<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        
        <androidx.cardview.widget.CardView
            android:id="@+id/searchBarCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            app:cardBackgroundColor="?attr/colorSurface"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="8dp"
                    android:src="@android:drawable/ic_menu_search"
                    app:tint="?android:attr/textColorSecondary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="Search conversations"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="16sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/searchBarCard">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/conversationsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                tools:listitem="@layout/item_conversation" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No conversations yet"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        
        <LinearLayout
            android:id="@+id/buttonsLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/emptyView"
            app:layout_constraintVertical_bias="0.1">

            <Button
                android:id="@+id/fixConversationsButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fix Conversations"
                android:backgroundTint="@color/primary"
                android:textColor="#FFFFFF"
                android:visibility="visible"
                android:layout_marginBottom="8dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/newConversationFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:backgroundTint="@color/primary"
        android:contentDescription="New Conversation"
        android:src="@android:drawable/ic_dialog_email"
        android:visibility="gone"
        app:borderWidth="0dp"
        app:tint="#FFFFFF" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
