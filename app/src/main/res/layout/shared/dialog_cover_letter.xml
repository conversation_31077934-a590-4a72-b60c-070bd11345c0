<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Cover Letter">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/coverLetterText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:inputType="textMultiLine"
            android:minLines="5"
            android:maxLines="10" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout> 