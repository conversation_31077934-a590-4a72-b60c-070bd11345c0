<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/colorOutline">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Certificate"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnRemoveCertificate"
                android:layout_width="36dp"
                android:layout_height="36dp"
                app:icon="@drawable/ic_delete"
                app:iconTint="@color/black"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                app:strokeWidth="0dp" />
        </LinearLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/certificateNameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Certificate Name"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <AutoCompleteTextView
                android:id="@+id/certificateName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Issuing Organization"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/certificateIssuer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Year Obtained"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/certificateYear"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:maxLength="4" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Description (Optional)"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/certificateDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="2"
                android:maxLines="4" />
        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
