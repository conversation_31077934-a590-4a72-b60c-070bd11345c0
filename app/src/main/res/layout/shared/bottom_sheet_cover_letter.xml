<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="?attr/colorSurface">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Cover Letter"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="?android:attr/textColorPrimary" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Tell us why you're the best fit for this position"
        android:textSize="14sp"
        android:textColor="?android:attr/textColorSecondary" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:hint="Your cover letter"
        app:endIconMode="clear_text"
        style="@style/Widget.CareerWorx.TextInputLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/coverLetterText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:inputType="textMultiLine"
            android:minLines="6"
            android:maxLines="10"
            style="@style/Widget.CareerWorx.TextInputEditText" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/submitButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:padding="16dp"
        android:text="Submit Application"
        android:textSize="16sp"
        app:cornerRadius="12dp"
        app:icon="@drawable/ic_send"
        app:iconGravity="textStart"
        app:backgroundTint="@color/primary" />

</LinearLayout>