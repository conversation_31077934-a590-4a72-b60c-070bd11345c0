<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingTop="4dp"
    android:paddingEnd="16dp"
    android:paddingBottom="4dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/messageCard"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        app:cardBackgroundColor="@color/primary"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/messageText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="260dp"
            android:padding="12dp"
            android:text="This is a user message"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/timeText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="12:34 PM"
        android:textColor="#757575"
        android:textSize="10sp"
        app:layout_constraintEnd_toEndOf="@id/messageCard"
        app:layout_constraintTop_toBottomOf="@id/messageCard" />

</androidx.constraintlayout.widget.ConstraintLayout>
