<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="140dp"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="2dp"
    app:strokeColor="@color/primary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="12dp"
        android:background="@drawable/certificate_badge_background">

        <!-- Certificate Icon -->
        <ImageView
            android:id="@+id/certificateIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_certificate"
            android:layout_marginBottom="8dp"
            app:tint="@color/primary" />

        <!-- Certificate Name -->
        <TextView
            android:id="@+id/certificateName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="AWS Certified"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginBottom="4dp" />

        <!-- Issuer -->
        <TextView
            android:id="@+id/certificateIssuer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Amazon"
            android:textSize="10sp"
            android:textColor="@color/text_secondary"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginBottom="4dp" />

        <!-- Year -->
        <TextView
            android:id="@+id/certificateYear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2023"
            android:textSize="10sp"
            android:textColor="@color/text_secondary"
            android:background="@drawable/year_badge_background"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
