<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?android:colorBackground"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_back"
        app:titleTextColor="@android:color/black"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:colorBackground"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Student Registration"
                android:textSize="32sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="8dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Create your account to find your dream job"
                android:textSize="16sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="32dp"/>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Personal Information"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="16dp"/>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Name"
                    android:inputType="textPersonName"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etSurname"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Surname"
                    android:inputType="textPersonName"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etCellNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Cell Number"
                    android:inputType="phone"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Email"
                    android:inputType="textEmailAddress"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black"
                app:passwordToggleEnabled="true"
                app:passwordToggleTint="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Password"
                    android:inputType="textPassword"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/provinceLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:hint="Province">

                <AutoCompleteTextView
                    android:id="@+id/provinceInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/cityLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="City">

                <AutoCompleteTextView
                    android:id="@+id/cityInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Professional Summary"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="16dp"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/yearsOfExperienceLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Years of Experience">

                <AutoCompleteTextView
                    android:id="@+id/yearsOfExperienceInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/certificateLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Certificate">

                <AutoCompleteTextView
                    android:id="@+id/certificateInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/expectedSalaryLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Expected Salary">

                <AutoCompleteTextView
                    android:id="@+id/expectedSalaryInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/fieldLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Field">

                <AutoCompleteTextView
                    android:id="@+id/fieldInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/subFieldLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Specialization">

                <AutoCompleteTextView
                    android:id="@+id/subFieldInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etSummary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Write a brief summary about yourself"
                    android:inputType="textMultiLine"
                    android:minLines="3"
                    android:gravity="top"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Work Experience"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="16dp"/>

            <LinearLayout
                android:id="@+id/experienceContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAddExperience"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add Experience"
                android:textColor="@android:color/white"
                app:backgroundTint="@android:color/black"
                android:layout_marginBottom="24dp"/>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Education"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="16dp"/>

            <LinearLayout
                android:id="@+id/educationContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAddEducation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add Education"
                android:textColor="@android:color/white"
                app:backgroundTint="@android:color/black"
                android:layout_marginBottom="24dp"/>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Skills"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="8dp"/>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <AutoCompleteTextView
                    android:id="@+id/etSkills"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Select skills"
                    android:inputType="none"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/skillsChipGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"/>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Social Links"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="16dp"/>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etLinkedin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="LinkedIn Profile"
                    android:inputType="textUri"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etGithub"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="GitHub Profile"
                    android:inputType="textUri"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:boxStrokeColor="@android:color/black"
                app:hintTextColor="@android:color/black">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPortfolio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Portfolio Website"
                    android:inputType="textUri"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="References"
                android:textSize="20sp"
                android:textStyle="bold"
                style="@style/Widget.CareerWorx.TextInputEditText"
                android:layout_marginBottom="16dp"/>

            <LinearLayout
                android:id="@+id/referencesContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAddReference"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add Reference"
                android:textColor="@android:color/white"
                app:backgroundTint="@android:color/black"
                android:layout_marginBottom="32dp"/>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnRegister"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Register"
                android:textColor="@android:color/white"
                app:backgroundTint="@android:color/black"
                android:padding="12dp"/>

        </LinearLayout>
    </ScrollView>
</LinearLayout>