<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/colorOutline">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etReferenceName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Reference Name"
                android:inputType="textPersonName"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etReferencePosition"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Position"
                android:inputType="text"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etReferenceCompany"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Company"
                android:inputType="text"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etReferenceEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Email"
                android:inputType="textEmailAddress"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etReferencePhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Phone Number"
                android:inputType="phone"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRemoveReference"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Remove Reference"
            android:textColor="@android:color/white"
            app:backgroundTint="@android:color/black"
            android:layout_gravity="end"/>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>