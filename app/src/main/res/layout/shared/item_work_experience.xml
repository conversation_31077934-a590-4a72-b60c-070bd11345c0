<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Company">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/companyInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Position">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/positionInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"/>

        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:hint="Start Date">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/startDateInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="date"/>

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:hint="End Date">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/endDateInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="date"/>

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Description">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/descriptionInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="3"/>

        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView> 