<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">


    <include
        android:id="@+id/toolbar_layout"
        layout="@layout/layout_admin_toolbar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <include
            android:id="@+id/search_layout"
            layout="@layout/layout_admin_search" />


        <LinearLayout
            android:id="@+id/emptyStateView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@android:drawable/ic_menu_search"
                android:tint="?android:attr/textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="No companies found"
                android:textColor="?android:attr/textColorPrimary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Try adjusting your search or add a new company"
                android:textAlignment="center"
                android:textColor="#666666" />
        </LinearLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/adminCompaniesRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:padding="8dp" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progressIndicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />
        </FrameLayout>


        <include
            android:id="@+id/pagination_layout"
            layout="@layout/layout_admin_pagination" />
    </LinearLayout>


    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addCompanyFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="32dp"
        android:src="@android:drawable/ic_input_add"
        app:backgroundTint="#2196F3"
        app:tint="#FFFFFF" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>