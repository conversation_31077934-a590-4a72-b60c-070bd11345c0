<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/primary">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/adminToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:titleTextColor="?attr/colorOnPrimary"
        app:navigationIconTint="?attr/colorOnPrimary"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

        <TextView
            android:id="@+id/adminToolbarTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Admin Panel"
            android:textColor="?attr/colorOnPrimary"
            android:textSize="20sp"
            android:textStyle="bold" />

    </androidx.appcompat.widget.Toolbar>

</com.google.android.material.appbar.AppBarLayout>
