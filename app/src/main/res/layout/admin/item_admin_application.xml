<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:contentDescription="Long press to edit application">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/jobTitleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginBottom="4dp"/>

        <TextView
            android:id="@+id/companyNameTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginBottom="4dp"/>

        <TextView
            android:id="@+id/applicantNameTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginBottom="4dp"/>

        <TextView
            android:id="@+id/dateTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginBottom="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <com.google.android.material.chip.Chip
                android:id="@+id/statusChip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                app:chipMinHeight="32dp"
                app:chipBackgroundColor="@color/status_pending"
                android:textColor="@color/white"/>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/viewDetailsButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Edit"
                android:textColor="@color/white"
                app:backgroundTint="@color/black"
                app:cornerRadius="4dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>