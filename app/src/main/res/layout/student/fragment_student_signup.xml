<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Student Registration"
            android:textSize="24sp"
            android:textStyle="bold"
            style="@style/Widget.CareerWorx.TextInputEditText"
            android:layout_marginBottom="8dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Create your account to find your dream job"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginBottom="24dp"/>

        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Personal Information"
            android:textSize="18sp"
            android:textStyle="bold"
            style="@style/Widget.CareerWorx.TextInputEditText"
            android:layout_marginBottom="16dp"/>

        
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Name"
                android:inputType="textPersonName"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etSurname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Surname"
                android:inputType="textPersonName"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Email"
                android:inputType="textEmailAddress"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.CareerWorx.TextInputLayout"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Password"
                android:inputType="textPassword"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRegister"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Register"
            android:textColor="@android:color/white"
            app:backgroundTint="@color/primary"
            android:padding="12dp"
            android:layout_marginTop="16dp"/>

    </LinearLayout>
</ScrollView>
