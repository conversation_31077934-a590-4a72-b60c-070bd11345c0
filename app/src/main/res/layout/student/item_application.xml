<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    app:cardBackgroundColor="?attr/colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/jobTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    style="@style/Widget.CareerWorx.TextInputEditText"
                    android:maxLines="1"
                    android:ellipsize="end"/>

                <TextView
                    android:id="@+id/companyName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="?android:attr/textColorSecondary"
                    android:layout_marginTop="4dp"
                    android:maxLines="1"
                    android:ellipsize="end"/>

            </LinearLayout>

            <com.google.android.material.chip.Chip
                android:id="@+id/statusChip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                app:chipBackgroundColor="@color/status_pending"
                android:textColor="@color/white"/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?attr/colorOutline"
            android:layout_marginVertical="12dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Applied: "
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary"/>

                    <TextView
                        android:id="@+id/appliedDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:maxLines="1"
                        android:ellipsize="end"/>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Location: "
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary"/>

                    <TextView
                        android:id="@+id/jobLocation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:maxLines="1"
                        android:ellipsize="end"/>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Salary: "
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary"/>

                    <TextView
                        android:id="@+id/jobSalary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:maxLines="1"
                        android:ellipsize="end"/>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Type: "
                        android:textSize="12sp"
                        android:textColor="?android:attr/textColorSecondary"/>

                    <TextView
                        android:id="@+id/jobType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:maxLines="1"
                        android:ellipsize="end"/>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/viewDetailsButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="View Details"
            android:textSize="14sp"
            android:layout_marginTop="12dp"
            app:cornerRadius="8dp"
            android:textColor="?attr/colorOnPrimary"
            app:backgroundTint="@color/primary"/>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>