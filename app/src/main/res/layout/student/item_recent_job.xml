<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/jobTitleText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Job Title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:maxLines="2"
                android:ellipsize="end" />

            <LinearLayout
                android:id="@+id/matchContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <TextView
                    android:id="@+id/matchPercentageText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:text="85%"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Match"
                    android:textSize="8sp"
                    android:textColor="@color/black"/>

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/companyNameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Company Name"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/jobLocationText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Location"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/postedDateText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Posted 2 days ago"
            android:textSize="12sp"
            android:textColor="@color/black"
            android:layout_marginTop="8dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/jobTypeChip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Full-time"
            android:layout_marginTop="8dp"
            app:chipBackgroundColor="@color/primary_light"
            android:textColor="@color/primary" />

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>