<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar.Company"
            app:title="Company Dashboard"
            app:titleTextAppearance="@style/TextAppearance.AppCompat.Medium" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="80dp"
            android:clipToPadding="false"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                
                <TextView
                    android:id="@+id/welcomeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Welcome back!"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="24dp"/>

                
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="20dp"
                            android:background="@color/primary">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Active Jobs"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"/>

                            <TextView
                                android:id="@+id/activeJobsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@android:color/white"
                                android:textSize="32sp"
                                android:textStyle="bold"
                                android:layout_marginTop="8dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="20dp"
                            android:background="@color/primary">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Applications"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"/>

                            <TextView
                                android:id="@+id/applicationsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@android:color/white"
                                android:textSize="32sp"
                                android:textStyle="bold"
                                android:layout_marginTop="8dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Quick Actions"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btnPostJob"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="20dp"
                            android:background="?attr/colorSurface">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@drawable/ic_add"
                                app:tint="?attr/colorOnSurface"
                                android:layout_marginBottom="12dp"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Post Job"
                                android:textSize="16sp"
                                android:textColor="@color/primary"
                                android:gravity="center"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btnViewApplications"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="20dp"
                            android:background="?attr/colorSurface">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@drawable/ic_application"
                                app:tint="?attr/colorOnSurface"
                                android:layout_marginBottom="12dp"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Applications"
                                android:textSize="16sp"
                                android:textColor="@color/primary"
                                android:gravity="center"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btnSearchCandidates"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="20dp"
                            android:background="?attr/colorSurface">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@drawable/ic_search"
                                app:tint="?attr/colorOnSurface"
                                android:layout_marginBottom="12dp"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Search Candidates"
                                android:textSize="16sp"
                                android:textColor="@color/primary"
                                android:gravity="center"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btnMessages"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="20dp"
                            android:background="?attr/colorSurface">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@android:drawable/ic_dialog_email"
                                app:tint="?attr/colorOnSurface"
                                android:layout_marginBottom="12dp"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Messages"
                                android:textSize="16sp"
                                android:textColor="@color/primary"
                                android:gravity="center"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Recent Activity"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recentActivityRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:layout_marginBottom="24dp"/>

                <TextView
                    android:id="@+id/emptyActivityView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No recent activity to display"
                    android:textColor="@android:color/darker_gray"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:visibility="gone"
                    android:layout_marginBottom="24dp"/>

                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Recent Applications"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recentApplicationsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:layout_marginBottom="16dp"/>

                <TextView
                    android:id="@+id/emptyApplicationsView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No recent applications to display"
                    android:textColor="@android:color/darker_gray"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:visibility="gone"
                    android:layout_marginBottom="24dp"/>

                
                <LinearLayout
                    android:id="@+id/buttonsLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/fixConversationsButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Fix Conversations"
                        android:visibility="gone"/>
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="?android:colorBackground"
        app:menu="@menu/company_bottom_navigation"
        app:labelVisibilityMode="labeled"
        app:itemIconTint="@color/company_bottom_nav_item_color"
        app:itemTextColor="@color/company_bottom_nav_item_color"
        app:elevation="8dp"/>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
