<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="1dp"
            app:strokeColor="#E0E0E0">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/Widget.CareerWorx.TextInputLayout">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/searchInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Search jobs"
                        android:textColor="@color/black"
                        android:inputType="text"
                        android:imeOptions="actionSearch"/>

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/filterChipGroup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/activeChip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Active"
                        android:textColor="@color/black"
                        app:chipBackgroundColor="#F5F5F5"
                        app:chipStrokeWidth="1dp"
                        app:chipStrokeColor="#E0E0E0"/>

                    <com.google.android.material.chip.Chip
                        android:id="@+id/closedChip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Closed"
                        android:textColor="@color/black"
                        app:chipBackgroundColor="#F5F5F5"
                        app:chipStrokeWidth="1dp"
                        app:chipStrokeColor="#E0E0E0"/>

                </com.google.android.material.chip.ChipGroup>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/jobsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp"/>

        
        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="No jobs posted yet"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:visibility="gone"/>

    </LinearLayout>

    
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addJobFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:backgroundTint="@color/accent"
        app:tint="@color/white"/>

</androidx.coordinatorlayout.widget.CoordinatorLayout>