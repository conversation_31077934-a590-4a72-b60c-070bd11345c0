package com.example.jobrec.ui.theme
import com.example.jobrec.R
import androidx.compose.ui.graphics.Color
val Blue700 = Color(0xFF1976D2)
val Blue900 = Color(0xFF0D47A1)
val Blue100 = Color(0xFFBBDEFB)
val Blue200 = Color(0xFF90CAF9)
val Blue400 = Color(0xFF42A5F5)
val Blue500 = Color(0xFF2196F3)
val Blue300 = Color(0xFF64B5F6)
val Blue800 = Color(0xFF1565C0)
val Blue50 = Color(0xFFE3F2FD)
val LightBackground = Color(0xFFF5F7FA)
val DarkBackground = Color(0xFF121212)
val LightSurface = Color(0xFFFFFFFF)
val DarkSurface = Color(0xFF1E1E1E)
val LightTextPrimary = Color(0xFF263238)
val LightTextSecondary = Color(0xFF607D8B)
val DarkTextPrimary = Color(0xFFFFFFFF)
val DarkTextSecondary = Color(0xB3FFFFFF)
val StatusPending = Color(0xFFFFC107)
val StatusReviewed = Color(0xFF2196F3)
val StatusAccepted = Color(0xFF4CAF50)
val StatusRejected = Color(0xFFF44336)
val StatusShortlisted = Color(0xFF9C27B0)
val StatusInterviewing = Color(0xFF009688)
val StatusOffered = Color(0xFFFF9800)