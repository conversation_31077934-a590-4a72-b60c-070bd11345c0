#!/bin/bash

# CareerWorx Import Update Script
echo "Updating imports across all files..."

BASE_DIR="/Users/<USER>/Desktop/Kotlin App"
cd "$BASE_DIR"

# Function to update imports in a file
update_imports() {
    local file="$1"
    if [ -f "$file" ]; then
        echo "Updating imports in $file..."
        
        # Update admin imports
        sed -i '' 's/import com\.example\.jobrec\.AdminDashboardActivity/import com.example.jobrec.admin.activities.AdminDashboardActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminApplicationsActivity/import com.example.jobrec.admin.activities.AdminApplicationsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminCompaniesActivity/import com.example.jobrec.admin.activities.AdminCompaniesActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminJobsActivity/import com.example.jobrec.admin.activities.AdminJobsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminUsersActivity/import com.example.jobrec.admin.activities.AdminUsersActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminLoginActivity/import com.example.jobrec.admin.activities.AdminLoginActivity/g' "$file"
        
        # Update admin adapters
        sed -i '' 's/import com\.example\.jobrec\.AdminApplicationsAdapter/import com.example.jobrec.admin.adapters.AdminApplicationsAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminCompaniesAdapter/import com.example.jobrec.admin.adapters.AdminCompaniesAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminJobsAdapter/import com.example.jobrec.admin.adapters.AdminJobsAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminUsersAdapter/import com.example.jobrec.admin.adapters.AdminUsersAdapter/g' "$file"
        
        # Update admin dialogs
        sed -i '' 's/import com\.example\.jobrec\.AdminApplicationDetailsDialog/import com.example.jobrec.admin.dialogs.AdminApplicationDetailsDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminEditApplicationDialog/import com.example.jobrec.admin.dialogs.AdminEditApplicationDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminEditCompanyDialog/import com.example.jobrec.admin.dialogs.AdminEditCompanyDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminEditJobDialog/import com.example.jobrec.admin.dialogs.AdminEditJobDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminEditUserDialog/import com.example.jobrec.admin.dialogs.AdminEditUserDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.AdminJobDetailsDialog/import com.example.jobrec.admin.dialogs.AdminJobDetailsDialog/g' "$file"
        
        # Update company imports
        sed -i '' 's/import com\.example\.jobrec\.CompanyDashboardActivityNew/import com.example.jobrec.company.activities.CompanyDashboardActivityNew/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanyProfileActivity/import com.example.jobrec.company.activities.CompanyProfileActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanySignupActivity/import com.example.jobrec.company.activities.CompanySignupActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanyApplicationDetailsActivity/import com.example.jobrec.company.activities.CompanyApplicationDetailsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanyAnalyticsActivity/import com.example.jobrec.company.activities.CompanyAnalyticsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EditCompanyProfileActivity/import com.example.jobrec.company.activities.EditCompanyProfileActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.PostJobActivity/import com.example.jobrec.company.activities.PostJobActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EmployerJobsActivity/import com.example.jobrec.company.activities.EmployerJobsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EmployerAnalyticsActivity/import com.example.jobrec.company.activities.EmployerAnalyticsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CandidateSearchActivity/import com.example.jobrec.company.activities.CandidateSearchActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EditJobActivity/import com.example.jobrec.company.activities.EditJobActivity/g' "$file"
        
        # Update company fragments
        sed -i '' 's/import com\.example\.jobrec\.CompanyApplicationsFragment/import com.example.jobrec.company.fragments.CompanyApplicationsFragment/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanyJobsFragment/import com.example.jobrec.company.fragments.CompanyJobsFragment/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanyProfileFragment/import com.example.jobrec.company.fragments.CompanyProfileFragment/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CompanySignupFragment/import com.example.jobrec.company.fragments.CompanySignupFragment/g' "$file"
        
        # Update company adapters
        sed -i '' 's/import com\.example\.jobrec\.CandidateSearchAdapter/import com.example.jobrec.company.adapters.CandidateSearchAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EmployerJobsAdapter/import com.example.jobrec.company.adapters.EmployerJobsAdapter/g' "$file"
        
        # Update student imports
        sed -i '' 's/import com\.example\.jobrec\.HomeActivity/import com.example.jobrec.student.activities.HomeActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ProfileActivity/import com.example.jobrec.student.activities.ProfileActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.JobsActivity/import com.example.jobrec.student.activities.JobsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ApplicationsActivity/import com.example.jobrec.student.activities.ApplicationsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.MyApplicationsActivity/import com.example.jobrec.student.activities.MyApplicationsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.SavedJobsActivity/import com.example.jobrec.student.activities.SavedJobsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.SearchActivity/import com.example.jobrec.student.activities.SearchActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.JobDetailsActivity/import com.example.jobrec.student.activities.JobDetailsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ApplicationDetailsActivity/import com.example.jobrec.student.activities.ApplicationDetailsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.StudentApplicationDetailsActivity/import com.example.jobrec.student.activities.StudentApplicationDetailsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ViewCvActivity/import com.example.jobrec.student.activities.ViewCvActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.CVDetailsActivity/import com.example.jobrec.student.activities.CVDetailsActivity/g' "$file"
        
        # Update student fragments
        sed -i '' 's/import com\.example\.jobrec\.StudentSignupFragment/import com.example.jobrec.student.fragments.StudentSignupFragment/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.fragments\.JobsFragment/import com.example.jobrec.student.fragments.JobsFragment/g' "$file"
        
        # Update student adapters
        sed -i '' 's/import com\.example\.jobrec\.JobsAdapter/import com.example.jobrec.student.adapters.JobsAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.MyApplicationsAdapter/import com.example.jobrec.student.adapters.MyApplicationsAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.RecentJobsAdapter/import com.example.jobrec.student.adapters.RecentJobsAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.RecommendedJobsAdapter/import com.example.jobrec.student.adapters.RecommendedJobsAdapter/g' "$file"
        
        # Update student models
        sed -i '' 's/import com\.example\.jobrec\.Student/import com.example.jobrec.student.models.Student/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.StudentEducation/import com.example.jobrec.student.models.StudentEducation/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.StudentExperience/import com.example.jobrec.student.models.StudentExperience/g' "$file"
        
        # Update shared imports
        sed -i '' 's/import com\.example\.jobrec\.LoginActivity/import com.example.jobrec.shared.activities.LoginActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.SignupActivity/import com.example.jobrec.shared.activities.SignupActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.UnifiedSignupActivity/import com.example.jobrec.shared.activities.UnifiedSignupActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.SplashActivity/import com.example.jobrec.shared.activities.SplashActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ChatActivity/import com.example.jobrec.shared.activities.ChatActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ConversationsActivity/import com.example.jobrec.shared.activities.ConversationsActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ContactActivity/import com.example.jobrec.shared.activities.ContactActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.HelpActivity/import com.example.jobrec.shared.activities.HelpActivity/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.PdfViewerActivity/import com.example.jobrec.shared.activities.PdfViewerActivity/g' "$file"
        
        # Update shared models
        sed -i '' 's/import com\.example\.jobrec\.User/import com.example.jobrec.shared.models.User/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.Company/import com.example.jobrec.shared.models.Company/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.Job/import com.example.jobrec.shared.models.Job/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.Application/import com.example.jobrec.shared.models.Application/g' "$file"
        
        # Update shared adapters
        sed -i '' 's/import com\.example\.jobrec\.ApplicationAdapter/import com.example.jobrec.shared.adapters.ApplicationAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.UserApplicationsAdapter/import com.example.jobrec.shared.adapters.UserApplicationsAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EducationAdapter/import com.example.jobrec.shared.adapters.EducationAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ExperienceAdapter/import com.example.jobrec.shared.adapters.ExperienceAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.LanguageAdapter/import com.example.jobrec.shared.adapters.LanguageAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.ReferenceAdapter/import com.example.jobrec.shared.adapters.ReferenceAdapter/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.FilterDropdownAdapter/import com.example.jobrec.shared.adapters.FilterDropdownAdapter/g' "$file"
        
        # Update shared dialogs
        sed -i '' 's/import com\.example\.jobrec\.ApplicationDetailsDialog/import com.example.jobrec.shared.dialogs.ApplicationDetailsDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.EditApplicationDialog/import com.example.jobrec.shared.dialogs.EditApplicationDialog/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.UserApplicationDetailsDialog/import com.example.jobrec.shared.dialogs.UserApplicationDetailsDialog/g' "$file"
        
        # Update shared utils
        sed -i '' 's/import com\.example\.jobrec\.SpacingItemDecoration/import com.example.jobrec.shared.utils.SpacingItemDecoration/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.FirebaseHelper/import com.example.jobrec.shared.utils.FirebaseHelper/g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.FirestoreIndexManager/import com.example.jobrec.shared.utils.FirestoreIndexManager/g' "$file"
        
        # Update utils imports
        sed -i '' 's/import com\.example\.jobrec\.utils\./import com.example.jobrec.shared.utils./g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.adapters\./import com.example.jobrec.shared.adapters./g' "$file"
        sed -i '' 's/import com\.example\.jobrec\.models\./import com.example.jobrec.shared.models./g' "$file"
        
        echo "Updated imports in $file"
    fi
}

# Update imports in all Kotlin files
echo "Updating imports in all Kotlin files..."
find app/src/main/java -name "*.kt" -type f | while read file; do
    update_imports "$file"
done

echo "Import updates complete!"
echo "Project reorganization finished successfully!"
echo ""
echo "Summary of changes:"
echo "- Created organized package structure (admin, company, student, shared)"
echo "- Moved all files to appropriate packages"
echo "- Updated package declarations"
echo "- Updated all import statements"
echo "- Removed legacy and guide files"
echo ""
echo "Please run a clean build to ensure everything compiles correctly."
