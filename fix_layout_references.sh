#!/bin/bash

# CareerWorx Layout Reference Fix Script
echo "Fixing layout references..."

BASE_DIR="/Users/<USER>/Desktop/Kotlin App"
cd "$BASE_DIR"

# Move layout files back to main layout directory
# Android doesn't support subdirectories in res/layout, so we need to move them back
echo "Moving layout files back to main layout directory..."

# Move all layout files back to the main layout directory
find app/src/main/res/layout -name "*.xml" -type f | while read file; do
    if [[ "$file" != app/src/main/res/layout/*.xml ]]; then
        filename=$(basename "$file")
        mv "$file" "app/src/main/res/layout/$filename"
        echo "Moved $file to app/src/main/res/layout/$filename"
    fi
done

# Remove empty subdirectories
rmdir app/src/main/res/layout/admin 2>/dev/null || true
rmdir app/src/main/res/layout/company 2>/dev/null || true
rmdir app/src/main/res/layout/student 2>/dev/null || true
rmdir app/src/main/res/layout/shared 2>/dev/null || true

echo "Layout files moved back to main directory."
echo "Note: Android doesn't support subdirectories in res/layout."
echo "Layout organization should be done through naming conventions instead."
