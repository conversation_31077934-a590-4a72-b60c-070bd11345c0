#!/bin/bash

# CareerWorx Project Reorganization Script
echo "Starting CareerWorx project reorganization..."

# Set the base directory
BASE_DIR="/Users/<USER>/Desktop/Kotlin App"
cd "$BASE_DIR"

# Create new package structure
echo "Creating new package structure..."
mkdir -p app/src/main/java/com/example/jobrec/admin/{activities,adapters,dialogs,fragments}
mkdir -p app/src/main/java/com/example/jobrec/company/{activities,fragments,adapters,models}
mkdir -p app/src/main/java/com/example/jobrec/student/{activities,fragments,adapters,models}
mkdir -p app/src/main/java/com/example/jobrec/shared/{activities,fragments,adapters,models,dialogs,utils}

# Create layout subdirectories
echo "Creating layout subdirectories..."
mkdir -p app/src/main/res/layout/{admin,company,student,shared}

echo "Package structure created successfully!"

# Function to update package declaration and move file
update_and_move() {
    local file="$1"
    local new_package="$2"
    local destination="$3"

    if [ -f "$file" ]; then
        echo "Processing $file..."
        # Update package declaration
        sed -i '' "s/^package com\.example\.jobrec$/package $new_package/" "$file"
        # Move file
        mv "$file" "$destination"
        echo "Moved $file to $destination"
    else
        echo "Warning: $file not found"
    fi
}

echo "Moving admin files..."

# Admin Activities
update_and_move "app/src/main/java/com/example/jobrec/AdminDashboardActivity.kt" "com.example.jobrec.admin.activities" "app/src/main/java/com/example/jobrec/admin/activities/"
update_and_move "app/src/main/java/com/example/jobrec/AdminApplicationsActivity.kt" "com.example.jobrec.admin.activities" "app/src/main/java/com/example/jobrec/admin/activities/"
update_and_move "app/src/main/java/com/example/jobrec/AdminCompaniesActivity.kt" "com.example.jobrec.admin.activities" "app/src/main/java/com/example/jobrec/admin/activities/"
update_and_move "app/src/main/java/com/example/jobrec/AdminJobsActivity.kt" "com.example.jobrec.admin.activities" "app/src/main/java/com/example/jobrec/admin/activities/"
update_and_move "app/src/main/java/com/example/jobrec/AdminUsersActivity.kt" "com.example.jobrec.admin.activities" "app/src/main/java/com/example/jobrec/admin/activities/"
update_and_move "app/src/main/java/com/example/jobrec/AdminLoginActivity.kt" "com.example.jobrec.admin.activities" "app/src/main/java/com/example/jobrec/admin/activities/"

# Admin Adapters
update_and_move "app/src/main/java/com/example/jobrec/AdminApplicationsAdapter.kt" "com.example.jobrec.admin.adapters" "app/src/main/java/com/example/jobrec/admin/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/AdminCompaniesAdapter.kt" "com.example.jobrec.admin.adapters" "app/src/main/java/com/example/jobrec/admin/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/AdminJobsAdapter.kt" "com.example.jobrec.admin.adapters" "app/src/main/java/com/example/jobrec/admin/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/AdminUsersAdapter.kt" "com.example.jobrec.admin.adapters" "app/src/main/java/com/example/jobrec/admin/adapters/"

# Admin Dialogs
update_and_move "app/src/main/java/com/example/jobrec/AdminApplicationDetailsDialog.kt" "com.example.jobrec.admin.dialogs" "app/src/main/java/com/example/jobrec/admin/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/AdminEditApplicationDialog.kt" "com.example.jobrec.admin.dialogs" "app/src/main/java/com/example/jobrec/admin/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/AdminEditCompanyDialog.kt" "com.example.jobrec.admin.dialogs" "app/src/main/java/com/example/jobrec/admin/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/AdminEditJobDialog.kt" "com.example.jobrec.admin.dialogs" "app/src/main/java/com/example/jobrec/admin/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/AdminEditUserDialog.kt" "com.example.jobrec.admin.dialogs" "app/src/main/java/com/example/jobrec/admin/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/AdminJobDetailsDialog.kt" "com.example.jobrec.admin.dialogs" "app/src/main/java/com/example/jobrec/admin/dialogs/"

# Admin Fragments
update_and_move "app/src/main/java/com/example/jobrec/AdminCompaniesFragment.kt" "com.example.jobrec.admin.fragments" "app/src/main/java/com/example/jobrec/admin/fragments/"

echo "Moving company files..."

# Company Activities
update_and_move "app/src/main/java/com/example/jobrec/CompanyDashboardActivityNew.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/CompanyProfileActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/CompanySignupActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/CompanyApplicationDetailsActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/CompanyAnalyticsActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/EditCompanyProfileActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/PostJobActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/EmployerJobsActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/EmployerAnalyticsActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/CandidateSearchActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"
update_and_move "app/src/main/java/com/example/jobrec/EditJobActivity.kt" "com.example.jobrec.company.activities" "app/src/main/java/com/example/jobrec/company/activities/"

# Company Fragments
update_and_move "app/src/main/java/com/example/jobrec/CompanyApplicationsFragment.kt" "com.example.jobrec.company.fragments" "app/src/main/java/com/example/jobrec/company/fragments/"
update_and_move "app/src/main/java/com/example/jobrec/CompanyJobsFragment.kt" "com.example.jobrec.company.fragments" "app/src/main/java/com/example/jobrec/company/fragments/"
update_and_move "app/src/main/java/com/example/jobrec/CompanyProfileFragment.kt" "com.example.jobrec.company.fragments" "app/src/main/java/com/example/jobrec/company/fragments/"
update_and_move "app/src/main/java/com/example/jobrec/CompanySignupFragment.kt" "com.example.jobrec.company.fragments" "app/src/main/java/com/example/jobrec/company/fragments/"

# Company Adapters
update_and_move "app/src/main/java/com/example/jobrec/CandidateSearchAdapter.kt" "com.example.jobrec.company.adapters" "app/src/main/java/com/example/jobrec/company/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/EmployerJobsAdapter.kt" "com.example.jobrec.company.adapters" "app/src/main/java/com/example/jobrec/company/adapters/"

echo "Moving student files..."

# Student Activities
update_and_move "app/src/main/java/com/example/jobrec/HomeActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ProfileActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/JobsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ApplicationsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/MyApplicationsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/SavedJobsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/SearchActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/JobDetailsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ApplicationDetailsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/StudentApplicationDetailsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ViewCvActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"
update_and_move "app/src/main/java/com/example/jobrec/CVDetailsActivity.kt" "com.example.jobrec.student.activities" "app/src/main/java/com/example/jobrec/student/activities/"

# Student Fragments
update_and_move "app/src/main/java/com/example/jobrec/StudentSignupFragment.kt" "com.example.jobrec.student.fragments" "app/src/main/java/com/example/jobrec/student/fragments/"
update_and_move "app/src/main/java/com/example/jobrec/fragments/JobsFragment.kt" "com.example.jobrec.student.fragments" "app/src/main/java/com/example/jobrec/student/fragments/"

# Student Adapters
update_and_move "app/src/main/java/com/example/jobrec/JobsAdapter.kt" "com.example.jobrec.student.adapters" "app/src/main/java/com/example/jobrec/student/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/MyApplicationsAdapter.kt" "com.example.jobrec.student.adapters" "app/src/main/java/com/example/jobrec/student/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/RecentJobsAdapter.kt" "com.example.jobrec.student.adapters" "app/src/main/java/com/example/jobrec/student/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/RecommendedJobsAdapter.kt" "com.example.jobrec.student.adapters" "app/src/main/java/com/example/jobrec/student/adapters/"

# Student Models
update_and_move "app/src/main/java/com/example/jobrec/Student.kt" "com.example.jobrec.student.models" "app/src/main/java/com/example/jobrec/student/models/"
update_and_move "app/src/main/java/com/example/jobrec/StudentEducation.kt" "com.example.jobrec.student.models" "app/src/main/java/com/example/jobrec/student/models/"
update_and_move "app/src/main/java/com/example/jobrec/StudentExperience.kt" "com.example.jobrec.student.models" "app/src/main/java/com/example/jobrec/student/models/"

echo "Moving shared files..."

# Shared Activities
update_and_move "app/src/main/java/com/example/jobrec/LoginActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/SignupActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/UnifiedSignupActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/SplashActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ChatActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ConversationsActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/ContactActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/HelpActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"
update_and_move "app/src/main/java/com/example/jobrec/PdfViewerActivity.kt" "com.example.jobrec.shared.activities" "app/src/main/java/com/example/jobrec/shared/activities/"

# Shared Models
update_and_move "app/src/main/java/com/example/jobrec/User.kt" "com.example.jobrec.shared.models" "app/src/main/java/com/example/jobrec/shared/models/"
update_and_move "app/src/main/java/com/example/jobrec/Company.kt" "com.example.jobrec.shared.models" "app/src/main/java/com/example/jobrec/shared/models/"
update_and_move "app/src/main/java/com/example/jobrec/Job.kt" "com.example.jobrec.shared.models" "app/src/main/java/com/example/jobrec/shared/models/"
update_and_move "app/src/main/java/com/example/jobrec/Application.kt" "com.example.jobrec.shared.models" "app/src/main/java/com/example/jobrec/shared/models/"

# Shared Adapters
update_and_move "app/src/main/java/com/example/jobrec/ApplicationAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/UserApplicationsAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/EducationAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/ExperienceAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/LanguageAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/ReferenceAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"
update_and_move "app/src/main/java/com/example/jobrec/FilterDropdownAdapter.kt" "com.example.jobrec.shared.adapters" "app/src/main/java/com/example/jobrec/shared/adapters/"

# Shared Dialogs
update_and_move "app/src/main/java/com/example/jobrec/ApplicationDetailsDialog.kt" "com.example.jobrec.shared.dialogs" "app/src/main/java/com/example/jobrec/shared/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/EditApplicationDialog.kt" "com.example.jobrec.shared.dialogs" "app/src/main/java/com/example/jobrec/shared/dialogs/"
update_and_move "app/src/main/java/com/example/jobrec/UserApplicationDetailsDialog.kt" "com.example.jobrec.shared.dialogs" "app/src/main/java/com/example/jobrec/shared/dialogs/"

# Shared Utils
update_and_move "app/src/main/java/com/example/jobrec/SpacingItemDecoration.kt" "com.example.jobrec.shared.utils" "app/src/main/java/com/example/jobrec/shared/utils/"
update_and_move "app/src/main/java/com/example/jobrec/FirebaseHelper.kt" "com.example.jobrec.shared.utils" "app/src/main/java/com/example/jobrec/shared/utils/"
update_and_move "app/src/main/java/com/example/jobrec/FirestoreIndexManager.kt" "com.example.jobrec.shared.utils" "app/src/main/java/com/example/jobrec/shared/utils/"

# Move utils directory contents
if [ -d "app/src/main/java/com/example/jobrec/utils" ]; then
    echo "Moving utils directory contents..."
    mv app/src/main/java/com/example/jobrec/utils/* app/src/main/java/com/example/jobrec/shared/utils/ 2>/dev/null || true
    rmdir app/src/main/java/com/example/jobrec/utils 2>/dev/null || true
fi

# Move adapters directory contents
if [ -d "app/src/main/java/com/example/jobrec/adapters" ]; then
    echo "Moving adapters directory contents..."
    for file in app/src/main/java/com/example/jobrec/adapters/*.kt; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            sed -i '' "s/^package com\.example\.jobrec\.adapters$/package com.example.jobrec.shared.adapters/" "$file"
            mv "$file" "app/src/main/java/com/example/jobrec/shared/adapters/"
        fi
    done
    rmdir app/src/main/java/com/example/jobrec/adapters 2>/dev/null || true
fi

# Move models directory contents
if [ -d "app/src/main/java/com/example/jobrec/models" ]; then
    echo "Moving models directory contents..."
    for file in app/src/main/java/com/example/jobrec/models/*.kt; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            sed -i '' "s/^package com\.example\.jobrec\.models$/package com.example.jobrec.shared.models/" "$file"
            mv "$file" "app/src/main/java/com/example/jobrec/shared/models/"
        fi
    done
    rmdir app/src/main/java/com/example/jobrec/models 2>/dev/null || true
fi

echo "Moving fragments directory contents..."
if [ -d "app/src/main/java/com/example/jobrec/fragments" ]; then
    for file in app/src/main/java/com/example/jobrec/fragments/*.kt; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            if [[ "$filename" == "JobsFragment.kt" ]]; then
                sed -i '' "s/^package com\.example\.jobrec\.fragments$/package com.example.jobrec.student.fragments/" "$file"
                mv "$file" "app/src/main/java/com/example/jobrec/student/fragments/"
            else
                sed -i '' "s/^package com\.example\.jobrec\.fragments$/package com.example.jobrec.shared.fragments/" "$file"
                mv "$file" "app/src/main/java/com/example/jobrec/shared/fragments/"
            fi
        fi
    done
    rmdir app/src/main/java/com/example/jobrec/fragments 2>/dev/null || true
fi

echo "Removing legacy and guide files..."

# Remove legacy files
rm -f "app/src/main/java/com/example/jobrec/CompanyDashboardActivity.kt" 2>/dev/null || true
rm -f "app/src/main/java/com/example/jobrec/JobRecApp.kt" 2>/dev/null || true

# Remove guide files from root
rm -f "FIREBASE_STORAGE_SETUP.md" 2>/dev/null || true
rm -f "GEMINI_API_SETUP.md" 2>/dev/null || true
rm -f "HIERARCHICAL_LOCATION_SYSTEM.md" 2>/dev/null || true
rm -f "JOB_MATCHING_IMPROVEMENTS.md" 2>/dev/null || true
rm -f "LOCATION_IMPLEMENTATION_EXAMPLE.kt" 2>/dev/null || true
rm -f "STRICTER_ALGORITHM_CHANGES.md" 2>/dev/null || true

echo "Reorganization complete! Now updating imports..."
